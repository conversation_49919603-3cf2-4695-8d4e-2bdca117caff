"""
Excel文件处理服务
负责Excel文件的上传、读取、分析等操作
"""
import pandas as pd
import os
import uuid
import shutil
from typing import Dict, Any, Optional, Tuple
from fastapi import UploadFile, HTTPException

from config.settings import settings

class ExcelService:
    """Excel文件处理服务类"""
    
    @staticmethod
    async def upload_file(file: UploadFile) -> <PERSON>ple[str, str, Dict[str, Any]]:
        """
        上传Excel文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            Tuple[str, str, Dict]: (file_id, file_path, sheet_info)
            
        Raises:
            HTTPException: 文件格式错误或处理失败
        """
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="请上传Excel文件 (.xlsx 或 .xls)")
        
        # 生成唯一文件ID
        file_id = str(uuid.uuid4())
        file_path = f"{settings.upload_dir}/{file_id}_{file.filename}"
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 读取Excel文件，获取基本信息
        try:
            sheet_info = ExcelService.get_file_info(file_path)
        except Exception as e:
            # 如果读取失败，删除已上传的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=400, detail=f"读取Excel文件失败: {str(e)}")
        
        return file_id, file_path, sheet_info
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """
        获取Excel文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 包含sheet信息的字典
        """
        df_dict = pd.read_excel(file_path, sheet_name=None)
        sheet_info = {}
        
        for sheet_name, sheet_df in df_dict.items():
            sheet_info[sheet_name] = {
                "rows": len(sheet_df),
                "columns": len(sheet_df.columns),
                "column_names": list(sheet_df.columns)
            }
        
        return sheet_info
    
    @staticmethod
    def build_file_info_prompt(file_path: str) -> str:
        """
        构建文件信息提示字符串
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            str: 文件信息字符串
        """
        df_dict = pd.read_excel(file_path, sheet_name=None)
        
        file_info = "当前Excel文件信息:\\n"
        for sheet_name, df in df_dict.items():
            file_info += f"Sheet '{sheet_name}': {len(df)} 行, {len(df.columns)} 列\\n"
            file_info += f"列名: {list(df.columns)}\\n"
            if len(df) > 0:
                file_info += f"前几行数据预览:\\n{df.head(3).to_string()}\\n\\n"
        
        return file_info
    
    @staticmethod
    def find_uploaded_file(file_id: str) -> Optional[str]:
        """
        根据文件ID查找上传的文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            Optional[str]: 文件路径，如果找不到返回None
        """
        for filename in os.listdir(settings.upload_dir):
            if filename.startswith(file_id):
                return f"{settings.upload_dir}/{filename}"
        return None
    
    @staticmethod
    def copy_file_for_conversation(source_path: str, conversation_id: str) -> str:
        """
        为对话会话复制文件
        
        Args:
            source_path: 源文件路径
            conversation_id: 对话ID
            
        Returns:
            str: 新文件路径
        """
        conversation_file_path = f"{settings.conversation_dir}/{conversation_id}_current.xlsx"
        shutil.copy2(source_path, conversation_file_path)
        return conversation_file_path
    
    @staticmethod
    def execute_python_code(code: str, input_file_path: str, output_file_path: str) -> str:
        """
        执行Python代码处理Excel文件
        
        Args:
            code: 要执行的Python代码
            input_file_path: 输入文件路径
            output_file_path: 输出文件路径
            
        Returns:
            str: 执行输出
            
        Raises:
            Exception: 代码执行失败
        """
        import io
        import sys
        
        # 在安全的环境中执行代码
        exec_globals = {
            'pd': pd,
            'input_file_path': input_file_path,
            'output_file_path': output_file_path,
            '__builtins__': __builtins__,
            'os': os,
            'uuid': uuid
        }
        
        # 捕获执行输出
        captured_output = io.StringIO()
        old_stdout = sys.stdout
        
        try:
            # 重定向stdout到我们的StringIO对象
            sys.stdout = captured_output
            
            # 执行代码（不输出执行日志）
            exec(code, exec_globals)
            
            # 恢复stdout
            sys.stdout = old_stdout
            
            # 获取捕获的输出
            execution_output = captured_output.getvalue()
            return execution_output
            
        except Exception as e:
            # 恢复stdout
            sys.stdout = old_stdout
            raise e
        finally:
            captured_output.close() 