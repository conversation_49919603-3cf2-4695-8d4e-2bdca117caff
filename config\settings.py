"""
应用配置管理模块
负责管理环境变量、全局配置等
"""
import os
from typing import Dict, Any
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

class Settings:
    """应用配置类"""
    
    def __init__(self):
        # 全局配置存储 - 从环境变量读取默认值
        self.config: Dict[str, Any] = {
            "openai_api_url": os.getenv("OPENAI_API_URL", ""),
            "openai_api_key": os.getenv("OPENAI_API_KEY", ""), 
            "model_name": os.getenv("MODEL_NAME", "gpt-3.5-turbo")
        }
        
        # 应用配置
        self.app_title = "Excel对话编辑系统"
        self.app_description = "通过对话方式编辑Excel文件"
        self.host = "0.0.0.0"
        self.port = 63225
        
        # 目录配置
        self.upload_dir = "uploads"
        self.download_dir = "downloads"
        self.template_dir = "templates"
        self.static_dir = "static"
        self.conversation_dir = "conversations"
        
        # 创建必要的目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.upload_dir,
            self.download_dir,
            self.template_dir,
            self.static_dir,
            self.conversation_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def update_openai_config(self, api_url: str, api_key: str, model_name: str):
        """更新OpenAI配置"""
        self.config["openai_api_url"] = api_url
        self.config["openai_api_key"] = api_key
        self.config["model_name"] = model_name
    
    def reload_env_config(self):
        """重新加载环境变量配置"""
        load_dotenv(override=True)
        
        # 更新配置（优先使用已设置的配置，如果为空则从环境变量读取）
        if not self.config["openai_api_url"]:
            self.config["openai_api_url"] = os.getenv("OPENAI_API_URL", "")
        if not self.config["openai_api_key"]:
            self.config["openai_api_key"] = os.getenv("OPENAI_API_KEY", "")
        if not self.config["model_name"] or self.config["model_name"] == "gpt-3.5-turbo":
            self.config["model_name"] = os.getenv("MODEL_NAME", "gpt-3.5-turbo")
    
    def get_openai_config(self) -> Dict[str, str]:
        """获取OpenAI配置"""
        return {
            "api_url": self.config["openai_api_url"],
            "api_key": self.config["openai_api_key"],
            "model_name": self.config["model_name"]
        }
    
    def is_openai_configured(self) -> bool:
        """检查OpenAI是否已配置"""
        return bool(self.config["openai_api_key"] and self.config["openai_api_url"])

# 全局设置实例
settings = Settings() 