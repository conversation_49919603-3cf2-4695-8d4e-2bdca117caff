"""
AI服务模块
负责与AI模型的交互、代码分析等功能
"""
import requests
import json
from typing import List, Dict, Any
from fastapi import HTTPException

from config.settings import settings
from config.prompts import get_system_prompt, TEST_MESSAGE, DANGEROUS_KEYWORDS

class AIService:
    """AI服务类"""
    
    @staticmethod
    def test_connection() -> Dict[str, Any]:
        """
        测试OpenAI连接
        
        Returns:
            Dict: 测试结果
        """
        if not settings.is_openai_configured():
            raise HTTPException(status_code=400, detail="请先配置API参数")
        
        config = settings.get_openai_config()
        
        print(f"[调试] 测试连接 - API URL: {config['api_url']}")
        print(f"[调试] 测试连接 - 模型: {config['model_name']}")
        print(f"[调试] 测试连接 - API Key前缀: {config['api_key'][:20]}...")
        
        # 构建请求
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config["model_name"],
            "messages": [{"role": "user", "content": TEST_MESSAGE}],
            "max_tokens": 10
        }
        
        print(f"[调试] 发送请求到: {config['api_url']}")
        
        try:
            response = requests.post(
                config["api_url"],
                headers=headers,
                json=data,
                timeout=30
            )
            
            print(f"[调试] 响应状态码: {response.status_code}")
            print(f"[调试] 响应内容: {response.text[:500]}...")
            
            if response.status_code == 200:
                return {"success": True, "message": "连接测试成功"}
            else:
                error_detail = f"状态码: {response.status_code}\n响应内容: {response.text[:200]}"
                return {"success": False, "message": f"连接失败:\n{error_detail}"}
                
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求错误: {str(e)}"
            print(f"[调试] {error_msg}")
            return {"success": False, "message": error_msg}
        except Exception as e:
            error_msg = f"测试连接失败: {str(e)}"
            print(f"[调试] {error_msg}")
            return {"success": False, "message": error_msg}
    
    @staticmethod
    def chat_with_ai(messages: List[Dict[str, str]], file_info: str) -> str:
        """
        与AI进行对话
        
        Args:
            messages: 消息历史列表（不包含系统提示词）
            file_info: Excel文件信息
            
        Returns:
            str: AI响应内容
            
        Raises:
            HTTPException: API调用失败
        """
        # 重新加载配置
        settings.reload_env_config()
        
        if not settings.is_openai_configured():
            error_message = "请先配置OpenAI API。"
            error_message += "\\n\\n您可以通过以下方式配置："
            error_message += "\\n1. 在界面上方的配置区域中输入API信息"
            error_message += "\\n2. 在项目根目录创建.env文件，包含："
            error_message += "\\n   OPENAI_API_URL=你的API地址"
            error_message += "\\n   OPENAI_API_KEY=你的API密钥"
            error_message += "\\n   MODEL_NAME=模型名称"
            raise HTTPException(status_code=400, detail=error_message)
        
        config = settings.get_openai_config()
        
        # 打印对话历史信息，帮助调试
        print(f"[调试] 历史对话消息数量: {len(messages)}")
        if messages:
            print(f"[调试] 对话历史概览:")
            for i, msg in enumerate(messages):
                content_preview = msg['content'][:100] + "..." if len(msg['content']) > 100 else msg['content']
                print(f"  {i+1}. {msg['role']}: {content_preview}")
        else:
            print(f"[调试] 这是新对话的第一条消息")
        
        # 构建完整的消息列表，包含系统提示
        system_prompt = get_system_prompt(file_info)
        full_messages = [{"role": "system", "content": system_prompt}] + messages
        
        print(f"[调试] 发送给AI的完整消息数量: {len(full_messages)} (包括系统提示词)")
        
        # 调用OpenAI API
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config["model_name"],
            "messages": full_messages,
            "max_tokens": 2000,
            "temperature": 0.1
        }
        
        response = requests.post(
            config["api_url"],
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=500, detail=f"AI调用失败: {response.text}")
        
        ai_response = response.json()
        print(f"[调试] AI原始响应: {json.dumps(ai_response, indent=2, ensure_ascii=False)}")

        if not ai_response.get("choices") or \
           not isinstance(ai_response["choices"], list) or \
           len(ai_response["choices"]) == 0 or \
           not ai_response["choices"][0].get("message") or \
           not ai_response["choices"][0]["message"].get("content"):
            error_msg = f"AI响应格式不正确或内容缺失: {json.dumps(ai_response, ensure_ascii=False)}"
            print(f"[调试] {error_msg}")
            raise HTTPException(status_code=500, detail=error_msg)
            
        return ai_response["choices"][0]["message"]["content"].strip()
    
    @staticmethod
    def extract_python_code(ai_response: str) -> str:
        """
        从AI响应中提取Python代码
        
        Args:
            ai_response: AI响应内容
            
        Returns:
            str: 提取的Python代码
            
        Raises:
            HTTPException: 代码提取失败
        """
        start_marker = "```python"
        end_marker = "```"
        
        start_index = ai_response.find(start_marker)
        
        if start_index == -1:
            raise HTTPException(status_code=500, detail="AI响应中未找到Python代码块")
        
        actual_code_start_index = start_index + len(start_marker)
        # 处理标记后的可选换行符
        if actual_code_start_index < len(ai_response) and ai_response[actual_code_start_index] == '\n':
            actual_code_start_index += 1
        
        # 从代码开始处查找结束标记
        end_index = ai_response.find(end_marker, actual_code_start_index)
        
        if end_index != -1:
            python_code = ai_response[actual_code_start_index:end_index].strip()
        else:
            # 找到开始标记但没有结束标记，假设代码到末尾
            python_code = ai_response[actual_code_start_index:].strip()
            # 如果末尾是 ```（因为前面没有换行符）
            if python_code.endswith(end_marker):
                python_code = python_code[:-len(end_marker)].strip()
        
        # 验证提取的代码是否有效
        if not python_code.strip():
            raise HTTPException(status_code=500, detail="提取到的Python代码为空")
        
        return python_code
    
    @staticmethod
    def analyze_code_for_file_modification(code: str) -> bool:
        """
        分析Python代码，判断是否会修改Excel文件
        
        Args:
            code: Python代码字符串
            
        Returns:
            bool: True表示会修改文件，False表示仅查询
        """
        code_lower = code.lower()
        
        # 文件修改操作的关键词
        modification_keywords = [
            # 数据框修改操作
            'df[', 'df.loc[', 'df.iloc[', 'df.at[', 'df.iat[',  # 赋值操作
            'df.drop(', 'df.insert(', 'df.pop(',  # 删除/插入操作
            'df.rename(', 'df.sort_values(', 'df.sort_index(',  # 重命名/排序
            'df.fillna(', 'df.replace(', 'df.dropna(',  # 数据清理
            'df.set_index(', 'df.reset_index(',  # 索引操作
            'df.assign(', 'df.eval(',  # 新列创建
            
            # 字典修改操作（针对多sheet）
            "df_dict['", 'df_dict["',  # 修改字典中的DataFrame
            
            # 数据合并/连接操作（通常会创建新的结构）
            'pd.concat(', 'pd.merge(', 'df.append(', 'df.join(',
            'pd.pivot_table(', 'df.pivot(', 'df.groupby(',
            
            # 明确的Excel写入操作
            'to_excel(', 'excelwriter', 'with pd.excelwriter',
            
            # 数学运算赋值（通常表示修改）
            ' = df', ' = pd', '= df', '= pd',  # 新变量赋值
        ]
        
        # 纯查询操作的关键词（这些操作不会修改原始数据）
        query_only_keywords = [
            # 统计查询
            '.sum()', '.mean()', '.median()', '.std()', '.var()',
            '.count()', '.nunique()', '.max()', '.min()',
            '.quantile(', '.describe()',
            
            # 信息查询
            '.head(', '.tail(', '.sample(', '.info()',
            '.shape', '.columns', '.index', '.dtypes',
            '.isnull()', '.isna()', '.notnull()', '.notna()',
            
            # 数据预览和检查
            'print(', 'len(', 'type(', 'str(',
            '.value_counts()', '.unique()',
            
            # 条件查询（不修改数据）
            '.query(', '.where(', '.loc[df[', '.iloc[df[',
        ]
        
        # 检查是否包含明确的保存操作
        has_save_operation = any(keyword in code_lower for keyword in [
            'to_excel(output_file_path', 'to_excel(output', 'excelwriter'
        ])
        
        # 如果没有保存操作，肯定不会修改文件
        if not has_save_operation:
            print(f"[分析] 代码中没有发现保存操作，判定为查询操作")
            return False
        
        # 检查是否包含修改操作
        has_modification = any(keyword in code_lower for keyword in modification_keywords)
        
        # 检查是否主要是查询操作
        query_count = sum(1 for keyword in query_only_keywords if keyword in code_lower)
        modification_count = sum(1 for keyword in modification_keywords if keyword in code_lower)
        
        print(f"[分析] 查询关键词数量: {query_count}, 修改关键词数量: {modification_count}")
        
        # 如果修改关键词较多，或者包含明确的修改操作，判定为修改
        if has_modification and modification_count > 0:
            # 进一步检查：如果只是简单的赋值给临时变量而没有回写到df_dict，可能是查询
            if ('result =' in code_lower or 'total_' in code_lower) and 'df_dict[' not in code_lower:
                print(f"[分析] 虽然有修改关键词，但可能是计算临时结果，需进一步判断")
                # 检查是否有回写操作
                has_writeback = any(pattern in code_lower for pattern in [
                    "df_dict['", 'df_dict["', 'sheet_df.to_excel', 'df.to_excel'
                ])
                return has_writeback
            return True
        
        # 如果主要是查询操作且没有明确的修改操作
        if query_count > modification_count and not has_modification:
            print(f"[分析] 主要是查询操作，无明确修改")
            return False
        
        # 默认情况：如果有保存操作但不确定，保守地认为是修改
        print(f"[分析] 无法明确判断，保守认为是修改操作")
        return True
    
    @staticmethod
    def check_code_safety(code: str) -> None:
        """
        检查代码安全性
        
        Args:
            code: Python代码字符串
        """
        for keyword in DANGEROUS_KEYWORDS:
            if keyword in code:
                print(f"[调试] 检测到潜在危险代码关键字: {keyword}")
                # 记录但不阻止，因为pandas可能需要一些这些操作 