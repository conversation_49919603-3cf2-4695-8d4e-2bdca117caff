"""
Excel对话编辑系统 - 重构后的主文件
使用模块化架构
"""
from fastapi import FastAPI, File, UploadFile, Request, HTTPException
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.templating import Jinja2Templates
import os
import uuid
import shutil
import traceback
from datetime import datetime
from typing import Optional, Dict, Any, List
import subprocess

# 导入自定义模块
from config.settings import settings
from models.schemas import (
    OpenAIConfig, ChatMessage, NewConversationRequest,
    FileUploadResponse, ChatResponse, ConfigStatusResponse
)
from services.excel_service import ExcelService
from services.ai_service import AIService

# 创建FastAPI应用
app = FastAPI(title=settings.app_title, description=settings.app_description)

# 模板配置
templates = Jinja2Templates(directory=settings.template_dir)

# 对话历史和会话管理
conversation_sessions: Dict[str, Dict[str, Any]] = {}

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页面"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/config/openai")
async def configure_openai(config_data: OpenAIConfig):
    """配置OpenAI API"""
    try:
        settings.update_openai_config(
            config_data.api_url,
            config_data.api_key,
            config_data.model_name
        )
        return {"success": True, "message": "配置保存成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置保存失败: {str(e)}")

@app.post("/config/test")
async def test_openai_connection():
    """测试OpenAI连接"""
    try:
        return AIService.test_connection()
    except HTTPException:
        raise
    except Exception as e:
        return {"success": False, "message": f"测试连接失败: {str(e)}"}

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """上传Excel文件"""
    try:
        file_id, file_path, sheet_info = await ExcelService.upload_file(file)
        
        return {
            "success": True,
            "file_id": file_id,
            "filename": file.filename,
            "sheet_info": sheet_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.post("/conversations/new")
async def create_new_conversation(request: NewConversationRequest):
    """创建新的对话会话"""
    try:
        # 查找上传的文件
        original_file_path = ExcelService.find_uploaded_file(request.file_id)
        if not original_file_path:
            raise HTTPException(status_code=404, detail="找不到对应的Excel文件")
        
        # 生成对话ID
        conversation_id = str(uuid.uuid4())
        
        # 为这个对话会话复制一份文件
        conversation_file_path = ExcelService.copy_file_for_conversation(
            original_file_path, conversation_id
        )
        
        # 获取原始文件名
        original_filename = os.path.basename(original_file_path).split("_", 1)[1]
        
        # 创建对话会话
        session = {
            "id": conversation_id,
            "file_id": request.file_id,
            "original_filename": original_filename,
            "created_at": datetime.now().isoformat(),
            "messages": [],
            "current_file_path": conversation_file_path
        }
        
        # 存储对话会话
        conversation_sessions[conversation_id] = session
        
        print(f"[调试] 创建新对话会话: {conversation_id}")
        
        return {
            "success": True,
            "conversation_id": conversation_id,
            "message": "新对话已创建"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[调试] 创建对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建对话失败: {str(e)}")

@app.get("/conversations")
async def get_conversations():
    """获取所有对话历史"""
    try:
        conversations = []
        for conv_id, session in conversation_sessions.items():
            conversations.append({
                "id": conv_id,
                "original_filename": session["original_filename"],
                "created_at": session["created_at"],
                "message_count": len(session["messages"])
            })
        
        # 按创建时间排序，最新的在前
        conversations.sort(key=lambda x: x["created_at"], reverse=True)
        
        return {
            "success": True,
            "conversations": conversations
        }
        
    except Exception as e:
        print(f"[调试] 获取对话历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取对话历史失败: {str(e)}")

@app.get("/conversations/{conversation_id}")
async def get_conversation_detail(conversation_id: str):
    """获取特定对话的详细信息"""
    try:
        if conversation_id not in conversation_sessions:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        session = conversation_sessions[conversation_id]
        
        return {
            "success": True,
            "conversation": session
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[调试] 获取对话详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取对话详情失败: {str(e)}")

@app.delete("/conversations/{conversation_id}")
async def delete_conversation(conversation_id: str):
    """删除对话会话"""
    try:
        if conversation_id not in conversation_sessions:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        session = conversation_sessions[conversation_id]
        
        # 删除对话对应的文件
        if os.path.exists(session["current_file_path"]):
            os.remove(session["current_file_path"])
        
        # 删除对话会话
        del conversation_sessions[conversation_id]
        
        print(f"[调试] 删除对话会话: {conversation_id}")
        
        return {
            "success": True,
            "message": "对话已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"[调试] 删除对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除对话失败: {str(e)}")

@app.post("/chat")
async def chat_with_ai(chat_data: ChatMessage):
    """与AI对话处理Excel"""
    try:
        # 确定使用的文件路径
        file_path: Optional[str] = None
        conversation_session: Optional[Dict[str, Any]] = None
        
        if chat_data.conversation_id:
            # 使用对话会话中的文件
            if chat_data.conversation_id not in conversation_sessions:
                raise HTTPException(status_code=404, detail="对话会话不存在")
            
            conversation_session = conversation_sessions[chat_data.conversation_id]
            file_path = conversation_session["current_file_path"]
            
            print(f"[调试] 使用对话会话文件: {file_path}")
            
            if not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail="对话会话文件不存在")
        else:
            # 使用原始上传的文件
            file_path = ExcelService.find_uploaded_file(chat_data.file_id)
            
            print(f"[调试] 使用原始上传文件: {file_path}")
            
            if not file_path:
                raise HTTPException(status_code=404, detail="找不到对应的Excel文件")
        
        # 构建文件信息提示
        file_info = ExcelService.build_file_info_prompt(file_path)
        
        print(f"[调试] 成功读取Excel文件信息")
        
        # 构建消息列表
        messages = []
        
        # 如果有对话会话，添加历史消息
        if conversation_session:
            for historical_msg in conversation_session["messages"]:
                messages.append({
                    "role": historical_msg["role"],
                    "content": historical_msg["content"]
                })
            
            print(f"[调试] 包含 {len(conversation_session['messages'])} 条历史消息")
        
        # 添加当前用户消息
        messages.append({"role": "user", "content": chat_data.message})
        
        # 调用AI服务
        ai_response_content = AIService.chat_with_ai(messages, file_info)
        
        print(f"[调试] AI响应内容 (处理前): '{ai_response_content}'")
        
        # 检查AI的响应是否是代码
        is_code_block = "```python" in ai_response_content

        if is_code_block:
            print(f"[调试] AI生成了代码，开始处理")
            
            # 提取Python代码
            python_code = AIService.extract_python_code(ai_response_content)
            print(f"[调试] 提取到的Python代码:\n{python_code}")
            
            # 分析代码是否会修改文件
            will_modify_file = AIService.analyze_code_for_file_modification(python_code)
            print(f"[调试] 代码分析结果 - 是否修改文件: {will_modify_file}")
            
            # 如果没有显式对话会话且代码会修改文件，则自动创建一个对话会话
            if not conversation_session and will_modify_file:
                # 复制当前文件到对话目录
                auto_conv_id = str(uuid.uuid4())
                conv_file_path = ExcelService.copy_file_for_conversation(file_path, auto_conv_id)
                
                # 初始化会话，并立即保存当前对话历史
                conversation_sessions[auto_conv_id] = {
                    "id": auto_conv_id,
                    "file_id": chat_data.file_id,
                    "original_filename": os.path.basename(file_path).split('_', 1)[1],
                    "created_at": datetime.now().isoformat(),
                    "messages": [
                        {"role": "user", "content": chat_data.message},
                        {"role": "assistant", "content": ai_response_content}
                    ],
                    "current_file_path": conv_file_path
                }
                conversation_session = conversation_sessions[auto_conv_id]
                # 更新 chat_data.conversation_id 和文件路径，供后续使用
                chat_data.conversation_id = auto_conv_id
                file_path = conv_file_path
                print(f"[调试] 自动创建对话会话: {auto_conv_id}, 使用文件: {file_path}")
                print(f"[调试] 自动会话中保存对话历史，消息数: {len(conversation_session['messages'])}")
            else:
                # 如果有现有对话会话，保存对话历史
                if conversation_session:
                    conversation_session["messages"].append({
                        "role": "user",
                        "content": chat_data.message
                    })
                    conversation_session["messages"].append({
                        "role": "assistant",
                        "content": ai_response_content
                    })
                    print(f"[调试] 已保存对话历史，当前消息数: {len(conversation_session['messages'])}")
            
            # 安全检查
            AIService.check_code_safety(python_code)
            
            # 执行生成的代码
            original_filename_base, original_filename_ext = os.path.splitext(os.path.basename(file_path))
            if '_' in original_filename_base and not conversation_session:
                original_filename_base = original_filename_base.split('_', 1)[1]

            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            
            # 根据是否修改文件和是否有对话会话来决定输出路径
            if conversation_session:
                if will_modify_file:
                    # 对话会话且修改文件：直接更新对话的当前文件
                    output_file_path = conversation_session["current_file_path"]
                    print(f"[调试] 对话会话中修改文件，更新当前文件: {output_file_path}")
                else:
                    # 对话会话中的查询操作：保存到临时位置
                    temp_query_filename = f"temp_{str(uuid.uuid4())}_query.xlsx"
                    output_file_path = os.path.join(settings.download_dir, temp_query_filename)
                    print(f"[调试] 对话会话中查询操作，结果保存到临时文件: {output_file_path}")
            else:
                if will_modify_file:
                    # 非对话会话且修改文件：保存到downloads供下载，使用新文件名格式
                    new_filename = f"{original_filename_base}_{timestamp}{original_filename_ext}"
                    output_file_path = os.path.join(settings.download_dir, new_filename)
                    print(f"[调试] 非对话会话修改文件，保存到downloads: {output_file_path}")
                else:
                    # 非对话会话且不修改文件：使用临时文件
                    temp_query_filename = f"temp_{str(uuid.uuid4())}_query.xlsx"
                    output_file_path = os.path.join(settings.download_dir, temp_query_filename)
                    print(f"[调试] 非对话会话查询操作，使用临时文件: {output_file_path}")
            
            # 执行代码
            try:
                execution_output = ExcelService.execute_python_code(
                    python_code, file_path, output_file_path
                )
                print(f"[调试] 代码执行输出: {execution_output}")
            except Exception as e:
                # 记录详细的错误信息用于调试
                error_details = f"代码执行错误: {str(e)}\n\n生成的代码:\n{python_code}\n\n错误堆栈:\n{traceback.format_exc()}"
                print(f"[调试] {error_details}")
                raise HTTPException(status_code=500, detail=f"代码执行失败: {str(e)}\n请检查AI生成的代码。")
            
            # 构建用户友好的结果消息
            result_message = execution_output.strip() if execution_output.strip() else "计算完成。"
            
            # 构建返回结果
            result = {
                "success": True,
                "type": "code_execution",
                "message": result_message,
                "generated_code": python_code,
                "execution_output": execution_output.strip(),
                "conversation_id": chat_data.conversation_id,
                "file_modified": will_modify_file
            }
            
            # 只有在文件真正被修改时才提供下载链接
            if will_modify_file:
                if not conversation_session:
                    # 如果不是对话会话且修改了文件，提供下载链接
                    result["download_url"] = f"/download/{os.path.basename(output_file_path)}"
                    result["message"] = f"处理完成，文件已保存为 {os.path.basename(output_file_path)}。您可以通过下载链接获取。"
                    print(f"[调试] 文件已修改，提供下载链接: {result['download_url']}，新文件名: {os.path.basename(output_file_path)}")
                else:
                    # 对话会话中，文件已更新，同时提供下载链接
                    # 生成一个用于下载的副本到downloads目录
                    original_filename_base = conversation_session["original_filename"].replace('.xlsx', '').replace('.xls', '')
                    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                    download_filename = f"{original_filename_base}_{timestamp}.xlsx"
                    download_file_path = os.path.join(settings.download_dir, download_filename)
                    
                    # 复制修改后的文件到downloads目录供下载
                    shutil.copy2(conversation_session["current_file_path"], download_file_path)
                    
                    result["download_url"] = f"/download/{download_filename}"
                    result["message"] = f"{result_message}\n\n📁 文件已修改，您可以下载更新后的文件。"
                    print(f"[调试] 对话会话中文件已更新，生成下载链接: {result['download_url']}")
            else:
                # 查询操作的处理
                if conversation_session:
                    # 对话会话中的查询：基于当前对话文件状态进行查询
                    print(f"[调试] 对话会话中查询操作完成，基于当前文件状态: {file_path}")
                else:
                    # 非对话会话的查询
                    print(f"[调试] 非对话会话查询操作完成，未修改文件")
                    
                # 清理临时文件
                if os.path.exists(output_file_path) and "temp_" in output_file_path:
                    try:
                        os.remove(output_file_path)
                        print(f"[调试] 清理临时查询文件: {output_file_path}")
                    except Exception as e:
                        print(f"[调试] 清理临时文件失败: {e}")

            return result
        else:
            # AI直接返回答案，也需要保存对话历史
            if conversation_session:
                conversation_session["messages"].append({
                    "role": "user",
                    "content": chat_data.message
                })
                conversation_session["messages"].append({
                    "role": "assistant",
                    "content": ai_response_content
                })
                print(f"[调试] 非代码回复，已保存对话历史，当前消息数: {len(conversation_session['messages'])}")
            
            print(f"[调试] AI直接返回答案: {ai_response_content}")
            return {
                "success": True,
                "type": "direct_answer",
                "message": ai_response_content,
                "conversation_id": chat_data.conversation_id
            }
        
    except HTTPException:
        raise
    except Exception as e:
        error_details = f"处理失败: {str(e)}\n错误堆栈:\n{traceback.format_exc()}"
        print(error_details)
        # 在这里不执行git commit，因为可能是请求处理过程中的错误，文件状态不确定
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/download/{file_id}")
async def download_file(file_id: str):
    """下载处理后的Excel文件"""
    try:
        # 新的逻辑：直接使用传入的文件名，支持带时间戳的文件名格式
        file_path = os.path.join(settings.download_dir, file_id)
        
        # 如果文件不存在，尝试旧的格式作为fallback
        if not os.path.exists(file_path):
            old_format_path = f"{settings.download_dir}/{file_id}_processed.xlsx"
            if os.path.exists(old_format_path):
                file_path = old_format_path
            else:
                raise HTTPException(status_code=404, detail="文件不存在")
        
        # 从文件名中提取显示名称
        display_filename = os.path.basename(file_path)
        
        return FileResponse(
            file_path,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename=display_filename
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")

@app.get("/download-conversation/{conversation_id}")
async def download_conversation_file(conversation_id: str):
    """下载对话会话的当前文件"""
    try:
        if conversation_id not in conversation_sessions:
            raise HTTPException(status_code=404, detail="对话会话不存在")
        
        session = conversation_sessions[conversation_id]
        current_file_path = session["current_file_path"]
        
        if not os.path.exists(current_file_path):
            raise HTTPException(status_code=404, detail="对话文件不存在")
        
        # 生成友好的文件名
        original_filename = session["original_filename"]
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        download_filename = f"{original_filename.replace('.xlsx', '').replace('.xls', '')}_{timestamp}.xlsx"
        
        return FileResponse(
            current_file_path,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename=download_filename
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载失败: {str(e)}")

@app.get("/config/status")
async def get_config_status():
    """获取当前配置状态"""
    try:
        # 重新加载.env文件
        settings.reload_env_config()
        
        # 检查各种配置来源
        env_config = {
            "api_url": os.getenv("OPENAI_API_URL", ""),
            "api_key": os.getenv("OPENAI_API_KEY", ""),
            "model_name": os.getenv("MODEL_NAME", "")
        }
        
        has_env_config = bool(env_config["api_url"] and env_config["api_key"])
        has_manual_config = settings.is_openai_configured()
        
        current_config = settings.get_openai_config()
        
        return {
            "success": True,
            "has_env_config": has_env_config,
            "has_manual_config": has_manual_config,
            "current_config": {
                "api_url": current_config["api_url"][:50] + "..." if len(current_config["api_url"]) > 50 else current_config["api_url"],
                "api_key_length": len(current_config["api_key"]),
                "model_name": current_config["model_name"]
            },
            "env_config": {
                "api_url": env_config["api_url"][:50] + "..." if len(env_config["api_url"]) > 50 else env_config["api_url"],
                "api_key_length": len(env_config["api_key"]),
                "model_name": env_config["model_name"]
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=settings.host, port=settings.port) 