"""
数据模型定义
包含所有Pydantic数据模型
"""
from pydantic import BaseModel
from typing import Optional, List, Dict
from datetime import datetime

class OpenAIConfig(BaseModel):
    """OpenAI配置模型"""
    api_url: str
    api_key: str
    model_name: str

class ChatMessage(BaseModel):
    """聊天消息模型"""
    message: str
    file_id: str
    conversation_id: Optional[str] = None

class ConversationSession(BaseModel):
    """对话会话模型"""
    id: str
    file_id: str
    original_filename: str
    created_at: datetime
    messages: List[Dict[str, str]]
    current_file_path: str  # 当前会话使用的文件路径

class NewConversationRequest(BaseModel):
    """新建对话请求模型"""
    file_id: str

class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    success: bool
    file_id: str
    filename: str
    sheet_info: Dict

class ChatResponse(BaseModel):
    """聊天响应模型"""
    success: bool
    type: str  # "code_execution" 或 "direct_answer"
    message: str
    conversation_id: Optional[str] = None
    generated_code: Optional[str] = None
    execution_output: Optional[str] = None
    download_id: Optional[str] = None
    download_url: Optional[str] = None
    file_modified: Optional[bool] = None

class ConfigStatusResponse(BaseModel):
    """配置状态响应模型"""
    success: bool
    has_env_config: bool
    has_manual_config: bool
    current_config: Dict
    env_config: Dict 