# Excel对话编辑系统

一个基于AI的Excel文件处理系统，支持通过自然语言对话来查询和修改Excel文件。

## 🆕 最新更新 - DeepSeek风格思考过程显示

系统现在采用了类似DeepSeek应用的思考过程显示方式：

### 新特性
- **嵌入式思考过程**：处理过程直接显示在AI回复消息中，而不是独立区域
- **自动展开/折叠**：处理中自动展开显示过程，完成后自动折叠
- **实时状态更新**：支持实时更新每个处理步骤的状态
- **耗时显示**：完成后显示总耗时（如：Thought for 3.2 seconds）
- **优雅的UI设计**：更加紧凑和直观的界面设计

### 显示效果
```
[用户消息]
[AI回复 - 带折叠的思考过程]
  ▼ Thought for 3.2 seconds
  1. ✅ 准备提交请求给AI模型
  2. ✅ AI模型已完成分析  
  3. ✅ AI响应处理完成 - 代码执行类型
  4. ✅ 生成的Python代码
  5. ✅ 代码执行输出
  6. ✅ 文件已修改，可以下载
  [AI最终回复内容]
```

## 主要功能

### 1. 文件上传与分析
- 支持 `.xlsx` 和 `.xls` 格式
- 自动分析文件结构（工作表、行数、列数、列名）
- 显示详细的文件信息

### 2. 自然语言对话
- 使用自然语言描述需求
- AI自动生成并执行Python代码
- 支持查询和修改操作

### 3. 对话历史管理
- 创建和管理多个对话会话
- 保存对话历史
- 支持恢复之前的对话

### 4. 代码执行与结果
- 自动生成pandas代码
- 安全的代码执行环境
- 实时显示执行结果

## 技术架构

- **后端**: FastAPI + Python
- **前端**: HTML + CSS + JavaScript
- **数据处理**: pandas + openpyxl
- **AI集成**: OpenAI API兼容接口

## 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量（可选）
cp .env.example .env
# 编辑 .env 文件设置API配置
```

### 2. 启动服务
```bash
python main.py
```

### 3. 访问系统
打开浏览器访问: http://localhost:63225

### 4. 使用步骤
1. **配置API**: 在"API配置"标签页设置OpenAI API信息
2. **上传文件**: 选择并上传Excel文件
3. **开始对话**: 用自然语言描述你的需求
4. **查看结果**: 观察思考过程和最终结果

## 使用示例

### 数据查询
```
"统计销售额大于1000的记录数量"
"找出销售额最高的前5条记录"
"计算每个地区的平均销售额"
```

### 数据修改
```
"将所有销售额小于500的记录标记为'低销售'"
"在最后一列添加利润率计算（销售额*0.2）"
"删除销售额为0的行"
```

### 数据分析
```
"生成销售额的统计摘要"
"按地区分组计算总销售额"
"找出异常的销售数据"
```

## 配置说明

### API配置
- **API URL**: OpenAI兼容的API端点
- **API Key**: 你的API密钥
- **模型名称**: 使用的模型（如gpt-3.5-turbo）

### 环境变量
```bash
OPENAI_API_URL=https://api.openai.com/v1/chat/completions
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL_NAME=gpt-3.5-turbo
```

## 项目结构

```
project/
├── main.py              # 主应用文件
├── config/              # 配置模块
├── models/              # 数据模型
├── services/            # 业务逻辑
├── templates/           # HTML模板
├── uploads/             # 上传文件目录
├── downloads/           # 下载文件目录
├── conversations/       # 对话文件目录
└── test_files/          # 测试文件
```

## 注意事项

1. **安全性**: 系统会检查生成的代码安全性
2. **文件大小**: 建议上传文件不超过10MB
3. **API限制**: 注意API调用频率和配额限制
4. **数据备份**: 重要数据请提前备份

## 更新日志

### v2.0.0 (最新)
- ✨ 实现DeepSeek风格的思考过程显示
- 🎨 优化UI设计，更加紧凑直观
- ⚡ 改进处理过程的实时反馈
- 🔧 移除独立的处理过程容器

### v1.0.0
- 🎉 初始版本发布
- 📁 文件上传和分析功能
- 💬 自然语言对话处理
- 📊 代码生成和执行
- 💾 对话历史管理

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License 