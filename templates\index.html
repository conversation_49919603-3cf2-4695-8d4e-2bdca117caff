<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel对话编辑系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .section {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 1.1em;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .header-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .section-content {
            padding: 20px;
            padding-bottom: 200px;
        }
        
        .fixed-bottom-chat-input {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            max-width: 800px;
            background-color: white;
            padding: 0;
            border: 1px solid #e5e5e5;
            border-radius: 20px;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            box-sizing: border-box;
        }
        
        .chat-input-container {
            position: relative;
            display: flex;
            align-items: flex-end;
            padding: 15px 20px;
            gap: 10px;
        }
        
        .chat-input-wrapper {
            flex: 1;
            position: relative;
        }
        
        .chat-textarea {
            width: 100%;
            padding: 12px 50px 12px 15px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            line-height: 1.4;
            resize: none;
            outline: none;
            background: transparent;
            min-height: 24px;
            max-height: 120px;
            overflow-y: auto;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .chat-textarea::placeholder {
            color: #999;
        }
        
        .send-button {
            position: absolute;
            right: 8px;
            bottom: 8px;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 8px;
            background: #007bff;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            font-size: 14px;
        }
        
        .send-button:hover {
            background: #0056b3;
            transform: scale(1.05);
        }
        
        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .send-arrow {
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 6px solid currentColor;
            transform: rotate(180deg);
        }
        
        .loading-spinner {
            width: 12px;
            height: 12px;
            border: 2px solid #fff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .message {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .message.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
        
        .file-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 0;
            margin: 10px 0;
            font-size: 13px;
            color: #6c757d;
            box-shadow: none;
            overflow: hidden;
        }
        
        .file-info-header {
            background: #e9ecef;
            color: #495057;
            padding: 12px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            transition: background-color 0.3s;
            user-select: none;
        }
        
        .file-info-header:hover {
            background: #dee2e6;
        }
        
        .file-info-toggle {
            font-size: 12px;
            transition: transform 0.3s;
        }
        
        .file-info-toggle.expanded {
            transform: rotate(180deg);
        }
        
        .file-info-content {
            padding: 12px;
            display: none;
        }
        
        .file-info-content.show {
            display: block;
        }
        
        .file-info h4 {
            color: #6c757d;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .sheet-info {
            margin-left: 12px;
            margin-bottom: 6px;
            font-size: 12px;
            color: #868e96;
        }
        
        .chat-box {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            height: 600px;
            overflow-y: auto;
            background: #f9f9f9;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chat-message {
            margin-bottom: 20px;
            padding: 16px;
            border-radius: 12px;
            line-height: 1.6;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }
        
        .chat-message.user {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 4px solid #2196f3;
            margin-left: 40px;
        }
        
        .chat-message.assistant {
            background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
            border-left: 4px solid #4caf50;
            margin-right: 40px;
        }
        
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .hidden {
            display: none;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Tab 样式 */
        .tab-container {
            margin-bottom: 20px;
        }
        
        .tab-buttons {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: #ecf0f1;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            color: #7f8c8d;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }
        
        .tab-button.active {
            background: #3498db;
            color: white;
            border-bottom: 3px solid #2980b9;
        }
        
        .tab-button:not(.active) {
            background: #ecf0f1;
            color: #7f8c8d;
            border-bottom: 3px solid transparent;
        }
        
        .tab-button:hover:not(.active) {
            background: #d5dbdb;
            color: #2c3e50;
        }
        
        .tab-content {
            display: none;
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .download-link {
            background: #27ae60;
            color: white !important;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            display: inline-block;
            margin: 5px 0;
            transition: background-color 0.3s;
        }
        
        .download-link:hover {
            background: #229954;
            text-decoration: none;
        }
        
        /* 对话历史相关样式 */
        .conversation-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s;
        }
        
        .conversation-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .conversation-title {
            font-weight: bold;
            color: #495057;
            font-size: 16px;
        }
        
        .conversation-meta {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .conversation-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .empty-conversations {
            text-align: center;
            color: #6c757d;
            padding: 40px;
            font-style: italic;
        }
        
        .current-conversation-badge {
            background: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        /* 嵌入式思考过程样式 */
        .thinking-process {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin: 10px 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .thinking-header {
            color: #666;
            font-size: 14px;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 6px 6px 0 0;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s;
        }

        .thinking-header:hover {
            background: #e9ecef;
        }

        .thinking-header.active {
            background: #e3f2fd;
        }

        .thinking-time {
            font-size: 12px;
            opacity: 0.9;
        }

        .thinking-toggle {
            font-size: 12px;
            transition: transform 0.3s;
        }

        .thinking-toggle.expanded {
            transform: rotate(180deg);
        }

        .thinking-content {
            display: none;
            padding: 12px;
            background: white;
            max-height: 300px;
            overflow-y: auto;
        }

        .thinking-content.show {
            display: block;
        }

        .thinking-spinner-text {
            color: #666;
            font-style: italic;
        }

        .thinking-step {
            margin-bottom: 8px;
            padding: 6px 10px;
            border-radius: 4px;
            display: flex;
            align-items: flex-start;
            font-size: 13px;
            line-height: 1.4;
        }

        .thinking-step.pending {
            background: #fff3cd;
            border-left: 3px solid #ffc107;
            color: #856404;
        }

        .thinking-step.processing {
            background: #d1ecf1;
            border-left: 3px solid #17a2b8;
            color: #0c5460;
        }

        .thinking-step.success {
            background: #d4edda;
            border-left: 3px solid #28a745;
            color: #155724;
        }

        .thinking-step.error {
            background: #f8d7da;
            border-left: 3px solid #dc3545;
            color: #721c24;
        }

        .thinking-step-icon {
            margin-right: 8px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .thinking-step-content {
            flex: 1;
        }

        .thinking-spinner {
            display: inline-block;
            width: 14px;
            height: 14px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #17a2b8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .thinking-code-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            margin-top: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 120px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .thinking-output {
            background: #272822;
            color: #f8f8f2;
            border-radius: 4px;
            padding: 8px;
            margin-top: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 100px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        /* 修改聊天消息样式以更好适配思考过程 */
        .chat-message.assistant {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
        }

        .chat-message.user {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
        }

        /* Markdown样式 */
        .markdown-content {
            line-height: 1.6;
        }
        
        .markdown-content h1, .markdown-content h2, .markdown-content h3 {
            margin-top: 20px;
            margin-bottom: 10px;
        }
        
        .markdown-content h1 {
            font-size: 1.8em;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 5px;
        }
        
        .markdown-content h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 3px;
        }
        
        .markdown-content h3 {
            font-size: 1.3em;
        }
        
        .markdown-content p {
            margin-bottom: 10px;
        }
        
        .markdown-content ul, .markdown-content ol {
            margin-bottom: 10px;
            padding-left: 25px;
        }
        
        .markdown-content li {
            margin-bottom: 5px;
        }
        
        .markdown-content blockquote {
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin: 10px 0;
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 0 4px 4px 0;
        }
        
        .markdown-content code {
            background: #f1f3f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        .markdown-content pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .markdown-content pre code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: 0.9em;
        }
        
        .markdown-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        
        .markdown-content th, .markdown-content td {
            border: 1px solid #e9ecef;
            padding: 8px 12px;
            text-align: left;
        }
        
        .markdown-content th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .markdown-content a {
            color: #3498db;
            text-decoration: none;
        }
        
        .markdown-content a:hover {
            text-decoration: underline;
        }
        
        .markdown-content hr {
            border: none;
            border-top: 1px solid #e9ecef;
            margin: 20px 0;
        }
        
        .markdown-content strong {
            font-weight: bold;
        }
        
        .markdown-content em {
            font-style: italic;
        }

        /* 其他页面元素的通用样式 */
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="password"], textarea:not(.chat-textarea), select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus, input[type="password"]:focus, textarea:not(.chat-textarea):focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
        }
        
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 2px dashed #ddd;
            border-radius: 4px;
            background: #fafafa;
        }
        
        button:not(.send-button):not(.tab-button) {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        button:not(.send-button):not(.tab-button):hover {
            background: #2980b9;
        }
        
        button:not(.send-button):not(.tab-button):disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-upload {
            background: #2ecc71;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
            display: inline-block;
            text-align: center;
            min-width: 200px;
        }
        
        .btn-upload:hover {
            background: #27ae60;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
    </style>
    <!-- 引入marked.js用于markdown解析 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>Excel对话编辑系统</h1>
        
        <!-- Tab导航 -->
        <div class="tab-container">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="switchTab('main')">🏠 主页面</button>
                <button class="tab-button" onclick="switchTab('conversations')">💬 对话历史</button>
                <!-- <button class="tab-button" onclick="switchTab('config')">⚙️ API配置</button> -->
            </div>
            
            <!-- 主页面Tab -->
            <div id="main-tab" class="tab-content active">
                <!-- 文件上传部分 -->
                <div class="section">
                    <div class="section-header">📤 上传Excel文件</div>
                    <div class="section-content">
                        <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;">
                        <button onclick="selectAndUploadFile()" class="btn-upload">选择并上传Excel文件</button>
                        <div id="uploadMessage"></div>
                        <div id="fileInfo" class="file-info hidden">
                            <div class="file-info-header" onclick="toggleFileInfo('fileInfo')">
                                <span>📄 文件信息</span>
                                <span class="file-info-toggle">▼</span>
                            </div>
                            <div class="file-info-content">
                                <div id="fileInfoContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 对话处理部分 -->
                <div class="section">
                    <div class="section-header">
                        <span>💬 与AI对话处理Excel</span>
                        <button id="newConversationBtn" onclick="createNewConversation()" class="header-button" style="display: none;">🆕 创建新对话</button>
                    </div>
                    <div class="section-content">
                        <!-- 删除对话上下文提示区域 -->
                        
                        <div id="chatBox" class="chat-box"></div>
                        
                        <div class="fixed-bottom-chat-input">
                            <div id="chatMessage" style="padding: 5px 20px;"></div>
                            <div class="chat-input-container">
                                <div class="chat-input-wrapper">
                                    <textarea id="userMessage" rows="4" placeholder="告诉我你的需求~" class="chat-textarea"></textarea>
                                    <button onclick="sendMessage()" id="sendBtn" class="send-button">
                                        <span class="send-arrow"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 对话历史Tab -->
            <div id="conversations-tab" class="tab-content">
                <div class="section">
                    <div class="section-header">📚 对话历史管理</div>
                    <div class="section-content">
                        <div style="margin-bottom: 15px;">
                            <button onclick="loadConversations()" class="btn-success">🔄 刷新列表</button>
                        </div>
                        <div id="conversationsList">
                            <p>正在加载对话历史...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 配置Tab - 隐藏 -->
            <div id="config-tab" class="tab-content" style="display: none;">
                <div class="section">
                    <div class="section-header">🔧 OpenAI API 配置</div>
                    <div class="section-content">
                        <div class="grid">
                            <div>
                                <div class="form-group">
                                    <label for="apiUrl">API URL:</label>
                                    <input type="text" id="apiUrl" placeholder="例如: https://api.openai.com/v1/chat/completions">
                                </div>
                                <div class="form-group">
                                    <label for="apiKey">API Key:</label>
                                    <input type="password" id="apiKey" placeholder="sk-...">
                                </div>
                                <div class="form-group">
                                    <label for="modelName">模型名称:</label>
                                    <input type="text" id="modelName" value="gpt-3.5-turbo" placeholder="gpt-3.5-turbo">
                                </div>
                            </div>
                            <div>
                                <div style="padding-top: 20px;">
                                    <button onclick="loadEnvConfig()" style="margin-right: 10px; background: #f39c12;">从.env加载配置</button>
                                    <button onclick="saveConfig()" style="margin-right: 10px;">保存配置</button>
                                    <button onclick="testConnection()" class="btn-success">测试连接</button>
                                </div>
                                <div id="configMessage"></div>
                                <div id="configStatus" style="margin-top: 10px; font-size: 12px; color: #666;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentFileId = null;
        let currentDownloadId = null;
        let currentConversationId = null;  // 当前对话ID
        
        // Tab切换功能
        function switchTab(tabName) {
            // 隐藏所有tab内容
            const allTabs = document.querySelectorAll('.tab-content');
            allTabs.forEach(tab => tab.classList.remove('active'));
            
            // 移除所有按钮的active类
            const allButtons = document.querySelectorAll('.tab-button');
            allButtons.forEach(btn => btn.classList.remove('active'));
            
            // 显示选中的tab
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活对应的按钮 - 通过onclick属性来找到对应按钮
            const targetButton = document.querySelector(`button[onclick="switchTab('${tabName}')"]`);
            if (targetButton) {
                targetButton.classList.add('active');
            }
            
            // 如果切换到对话历史tab，自动加载对话列表
            if (tabName === 'conversations') {
                loadConversations();
            }
        }
        
        // 定义 toggleFileInfo 函数
        function toggleFileInfo(elementId) {
            const fileInfoElement = document.getElementById(elementId);
            if (!fileInfoElement) return;

            const content = fileInfoElement.querySelector('.file-info-content');
            const toggle = fileInfoElement.querySelector('.file-info-toggle');

            if (content && toggle) {
                content.classList.toggle('show');
                toggle.classList.toggle('expanded');
            }
        }
        
        // 显示消息
        function showMessage(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="message ${type}">${message}</div>`;
            setTimeout(() => {
                element.innerHTML = '';
            }, 5000);
        }
        
        // 从.env加载配置
        async function loadEnvConfig() {
            try {
                showMessage('configMessage', '正在从.env文件加载配置...', 'info');
                const response = await fetch('/config/status', {
                    method: 'GET'
                });
                
                const result = await response.json();
                if (result.success && result.has_env_config) {
                    // 填充界面配置
                    document.getElementById('apiUrl').value = result.env_config.api_url;
                    document.getElementById('modelName').value = result.env_config.model_name || 'gpt-3.5-turbo';
                    
                    showMessage('configMessage', '已从.env文件加载配置！请输入API Key并保存。', 'success');
                    updateConfigStatus(result);
                } else if (result.success && !result.has_env_config) {
                    showMessage('configMessage', '未找到.env文件或文件中缺少必要配置', 'error');
                    updateConfigStatus(result);
                } else {
                    showMessage('configMessage', '加载配置失败: ' + result.error, 'error');
                }
            } catch (error) {
                showMessage('configMessage', '加载配置失败: ' + error.message, 'error');
            }
        }
        
        // 更新配置状态显示
        function updateConfigStatus(result) {
            const statusElement = document.getElementById('configStatus');
            let statusText = '';
            
            if (result.has_env_config) {
                statusText += '✅ .env配置已找到 ';
            } else {
                statusText += '❌ 未找到.env配置 ';
            }
            
            if (result.has_manual_config) {
                statusText += '✅ 界面配置已设置';
            } else {
                statusText += '❌ 界面配置未设置';
            }
            
            statusElement.innerHTML = statusText;
        }
        
        // 页面加载时自动检查配置状态
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化tab状态 - 确保只有主页面tab是激活的
            const allButtons = document.querySelectorAll('.tab-button');
            allButtons.forEach(btn => btn.classList.remove('active'));
            
            const mainTabButton = document.querySelector(`button[onclick="switchTab('main')"]`);
            if (mainTabButton) {
                mainTabButton.classList.add('active');
            }
            
            // 确保只有主页面tab内容显示
            const allTabs = document.querySelectorAll('.tab-content');
            allTabs.forEach(tab => tab.classList.remove('active'));
            
            const mainTabContent = document.getElementById('main-tab');
            if (mainTabContent) {
                mainTabContent.classList.add('active');
            }
            
            // 检查配置状态
            fetch('/config/status')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        updateConfigStatus(result);
                    }
                })
                .catch(error => {
                    console.log('检查配置状态失败:', error);
                });
                
            // 检查marked.js是否正确加载
            setTimeout(() => {
                console.log('marked.js检查:');
                console.log('typeof marked:', typeof marked);
                if (typeof marked !== 'undefined') {
                    console.log('marked.parse:', typeof marked.parse);
                    console.log('marked as function:', typeof marked === 'function');
                    
                    // 测试markdown解析
                    try {
                        let testText = '**粗体测试** 和 *斜体测试*';
                        let result = '';
                        if (typeof marked.parse === 'function') {
                            result = marked.parse(testText);
                        } else if (typeof marked === 'function') {
                            result = marked(testText);
                        }
                        console.log('测试markdown解析:', testText, '=>', result);
                    } catch (e) {
                        console.error('markdown测试失败:', e);
                    }
                } else {
                    console.error('marked.js未加载');
                }
            }, 1000);
                
            const fileInput = document.getElementById('excelFile');
            fileInput.addEventListener('change', function() {
                if (this.files[0]) {
                    uploadFile();
                }
            });
            
            // 聊天输入框增强功能
            const userMessageTextarea = document.getElementById('userMessage');
            
            // 自动调整textarea高度
            function autoResizeTextarea() {
                userMessageTextarea.style.height = 'auto';
                userMessageTextarea.style.height = Math.min(userMessageTextarea.scrollHeight, 120) + 'px';
            }
            
            // 监听输入事件，自动调整高度
            userMessageTextarea.addEventListener('input', autoResizeTextarea);
            
            // 监听键盘事件，支持Enter发送
            userMessageTextarea.addEventListener('keydown', function(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    sendMessage();
                } else if (event.key === 'Enter' && event.shiftKey) {
                    // Shift+Enter 换行，保持默认行为
                    setTimeout(autoResizeTextarea, 0);
                }
            });
            
            // 初始化高度
            autoResizeTextarea();
        });
        
        // 保存OpenAI配置
        async function saveConfig() {
            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const modelName = document.getElementById('modelName').value;
            
            if (!apiUrl || !apiKey || !modelName) {
                showMessage('configMessage', '请填写完整的配置信息', 'error');
                return;
            }
            
            try {
                const response = await fetch('/config/openai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        api_url: apiUrl,
                        api_key: apiKey,
                        model_name: modelName
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    showMessage('configMessage', '配置保存成功！', 'success');
                } else {
                    showMessage('configMessage', result.message, 'error');
                }
            } catch (error) {
                showMessage('configMessage', '保存配置失败: ' + error.message, 'error');
            }
        }
        
        // 测试连接
        async function testConnection() {
            try {
                showMessage('configMessage', '正在测试连接...', 'info');
                const response = await fetch('/config/test', {
                    method: 'POST'
                });
                
                const result = await response.json();
                if (result.success) {
                    showMessage('configMessage', result.message, 'success');
                } else {
                    showMessage('configMessage', result.message, 'error');
                }
            } catch (error) {
                showMessage('configMessage', '测试连接失败: ' + error.message, 'error');
            }
        }
        
        // 选择并上传文件
        function selectAndUploadFile() {
            const fileInput = document.getElementById('excelFile');
            fileInput.click();
        }
        
        // 上传文件
        async function uploadFile() {
            const fileInput = document.getElementById('excelFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showMessage('uploadMessage', '请选择一个Excel文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                showMessage('uploadMessage', '正在上传文件...', 'info');
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                if (result.success) {
                    currentFileId = result.file_id;
                    showMessage('uploadMessage', '文件上传成功！', 'success');
                    displayFileInfo(result);
                } else {
                    showMessage('uploadMessage', result.detail || '上传失败', 'error');
                }
            } catch (error) {
                showMessage('uploadMessage', '上传失败: ' + error.message, 'error');
            }
        }
        
        // 显示文件信息
        function displayFileInfo(result) {
            const fileInfo = document.getElementById('fileInfo');
            const fileInfoHeader = fileInfo.querySelector('.file-info-header');
            const fileInfoContent = document.getElementById('fileInfoContent');
            const toggle = fileInfo.querySelector('.file-info-toggle');
            const content = fileInfo.querySelector('.file-info-content');
            
            // 更新标题
            fileInfoHeader.innerHTML = `
                <span>📄 文件信息: ${result.filename}</span>
                <span class="file-info-toggle">▼</span>
            `;
            
            // 更新文件详细信息
            let contentHtml = '';
            
            for (const [sheetName, info] of Object.entries(result.sheet_info)) {
                contentHtml += `<div class="sheet-info">`;
                contentHtml += `<strong>Sheet "${sheetName}":</strong><br>`;
                contentHtml += `- 行数: ${info.rows}<br>`;
                contentHtml += `- 列数: ${info.columns}<br>`;
                contentHtml += `- 列名: ${info.column_names.join(', ')}<br>`;
                contentHtml += `</div>`;
            }
            
            fileInfoContent.innerHTML = contentHtml;
            
            // 确保默认是折叠状态
            content.classList.remove('show');
            toggle.classList.remove('expanded');
            
            // 显示文件信息区域
            fileInfo.classList.remove('hidden');
            
            // 上传成功后显示对话功能
            showConversationSection();
        }
        
        // 定义 showConversationSection 函数
        function showConversationSection() {
            // 获取所有 class="section" 的元素
            const sections = document.querySelectorAll('.section');
            // 对话处理部分通常是第二个 section 元素
            // （第一个是文件上传，第二个是对话处理）
            if (sections.length > 1) {
                const conversationSection = sections[1];
                // 确保该区域可见 (虽然它默认可能是可见的)
                // 如果它有 hidden class, 需要移除
                // conversationSection.classList.remove('hidden'); 
                // 如果它是通过 style.display 控制的
                conversationSection.style.display = 'block';
            }

            const newConversationButton = document.getElementById('newConversationBtn');
            if (newConversationButton) {
                newConversationButton.style.display = 'inline-block'; // 或者 'block', 根据布局需要
            }

            // 也可以考虑显示聊天输入框等，但它们默认应该是可见的
            // const chatBox = document.getElementById('chatBox');
            // if (chatBox) chatBox.style.display = 'block';
            // const chatInputArea = document.querySelector('.fixed-bottom-chat-input');
            // if (chatInputArea) chatInputArea.style.display = 'block'; // 或者 'flex' 等
        }
        
        // 检查AI返回结果是否包含文件修改
        function checkIfFileWasModified(result) {
            // 检查result对象中的file_modified字段
            if (result.hasOwnProperty('file_modified')) {
                return result.file_modified;
            }
            
            // 如果没有明确的file_modified字段，通过其他信息判断
            // 如果有download_url，通常意味着文件被修改了
            if (result.download_url) {
                return true;
            }
            
            // 如果类型是代码执行且有执行输出，可能修改了文件
            if (result.type === 'code_execution' && result.execution_output) {
                // 检查执行输出中是否包含文件保存相关的关键词
                const output = result.execution_output.toLowerCase();
                if (output.includes('保存') || output.includes('写入') || output.includes('save') || 
                    output.includes('已完成') || output.includes('成功')) {
                    return true;
                }
            }
            
            // 默认情况下假设没有修改文件
            return false;
        }
        
        // 发送消息函数
        async function sendMessage() {
            const message = document.getElementById('userMessage').value;
            const sendBtn = document.getElementById('sendBtn');
            
            if (!message.trim()) {
                showMessage('chatMessage', '请输入您的需求', 'error');
                return;
            }
            
            if (!currentFileId) {
                showMessage('chatMessage', '请先上传Excel文件', 'error');
                return;
            }
            
            // 添加用户消息到聊天框
            addMessageToChat('user', message);
            document.getElementById('userMessage').value = '';
            
            // 禁用发送按钮
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<span class="loading-spinner"></span>';
            
            // 记录开始时间
            const startTime = Date.now();
            
            // 创建AI消息占位符，包含思考过程
            const aiMessageId = createAIMessageWithThinking();
            
            try {
                // 步骤1: 准备请求
                addThinkingStep(aiMessageId, 'step1', 'pending', '📝 准备提交请求给AI模型');
                updateThinkingStep(aiMessageId, 'step1', 'processing', '📝 正在提交请求给AI模型');
                
                const requestBody = {
                    message: message,
                    file_id: currentFileId
                };
                
                // 如果有当前对话ID，添加到请求中
                if (currentConversationId) {
                    requestBody.conversation_id = currentConversationId;
                }
                
                updateThinkingStep(aiMessageId, 'step1', 'success', '✅ 请求已提交给AI模型');
                
                // 步骤2: 等待AI响应
                addThinkingStep(aiMessageId, 'step2', 'processing', '🤖 AI模型正在分析并生成回复');
                
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                
                updateThinkingStep(aiMessageId, 'step2', 'success', '✅ AI模型已完成分析');
                
                if (result.success) {
                    // 步骤3: 处理AI响应
                    addThinkingStep(aiMessageId, 'step3', 'processing', '📋 正在处理AI响应');
                    
                    // 根据返回类型构建AI回复消息
                    let aiMessage = result.message;
                    let hasFileModification = false;
                    
                    if (result.type === 'code_execution') {
                        // 检查是否有文件修改
                        hasFileModification = checkIfFileWasModified(result);
                        
                        updateThinkingStep(aiMessageId, 'step3', 'success', '✅ AI响应处理完成 - 代码执行类型');
                        
                        // 步骤4: 显示生成的代码
                        if (result.generated_code) {
                            addThinkingStep(aiMessageId, 'step4', 'success', '📄 生成的Python代码', result.generated_code);
                        }
                        
                        // 步骤5: 显示执行结果
                        if (result.execution_output) {
                            addThinkingStep(aiMessageId, 'step5', 'success', '💻 代码执行输出', result.execution_output);
                        }
                        
                        // 步骤6: 文件处理结果
                        if (hasFileModification) {
                            if (result.download_url) {
                                addThinkingStep(aiMessageId, 'step6', 'success', '📁 文件已修改，可以下载');
                                aiMessage += '<br><br>📁 <a href="' + result.download_url + '" class="download-link" target="_blank">下载处理后的文件</a>';
                            } else {
                                addThinkingStep(aiMessageId, 'step6', 'success', '📁 文件已修改');
                            }
                        } else {
                            addThinkingStep(aiMessageId, 'step6', 'success', '📊 仅查询操作，无文件修改');
                        }
                    } else {
                        updateThinkingStep(aiMessageId, 'step3', 'success', '✅ AI响应处理完成 - 直接回答类型');
                        addThinkingStep(aiMessageId, 'step4', 'success', '💬 AI直接回答，无需代码执行');
                    }
                    
                    // 计算耗时
                    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
                    
                    // 完成思考过程并自动折叠
                    completeThinkingProcess(aiMessageId, duration);
                    
                    // 更新AI消息内容
                    updateAIMessageContent(aiMessageId, aiMessage);
                    
                    // 更新对话ID（如果返回了新的对话ID）
                    if (result.conversation_id) {
                        currentConversationId = result.conversation_id;
                    }
                    
                    currentDownloadId = result.download_id;
                    showMessage('chatMessage', '处理完成！', 'success');
                } else {
                    updateThinkingStep(aiMessageId, 'step2', 'error', '❌ AI处理失败: ' + (result.detail || result.message));
                    
                    // 计算耗时
                    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
                    completeThinkingProcess(aiMessageId, duration);
                    
                    updateAIMessageContent(aiMessageId, '处理失败: ' + (result.detail || result.message));
                    showMessage('chatMessage', result.detail || result.message, 'error');
                }
            } catch (error) {
                // 如果有步骤2，更新为错误状态
                const step2Element = document.querySelector(`#${aiMessageId} .thinking-step[data-step="step2"]`);
                if (step2Element) {
                    updateThinkingStep(aiMessageId, 'step2', 'error', '❌ 网络错误或服务器异常: ' + error.message);
                } else {
                    addThinkingStep(aiMessageId, 'step2', 'error', '❌ 网络错误或服务器异常: ' + error.message);
                }
                
                // 计算耗时
                const duration = ((Date.now() - startTime) / 1000).toFixed(1);
                completeThinkingProcess(aiMessageId, duration);
                
                updateAIMessageContent(aiMessageId, '处理失败: ' + error.message);
                showMessage('chatMessage', '处理失败: ' + error.message, 'error');
            } finally {
                // 恢复发送按钮
                sendBtn.disabled = false;
                sendBtn.innerHTML = '<span class="send-arrow"></span>';
            }
        }

        // 创建带思考过程的AI消息
        function createAIMessageWithThinking() {
            const chatBox = document.getElementById('chatBox');
            const messageId = 'ai-message-' + Date.now();
            const messageDiv = document.createElement('div');
            messageDiv.id = messageId;
            messageDiv.className = 'chat-message assistant';
            
            // 格式化时间戳
            const timestamp = new Date().toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit' 
            });
            
            messageDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div style="flex: 1;">
                        <strong>🤖 AI助手:</strong>
                        <div class="thinking-process">
                            <div class="thinking-header active" onclick="toggleThinkingProcess('${messageId}')">
                                <span class="thinking-spinner-text">💭 Thinking...</span>
                                <span class="thinking-toggle expanded">▼</span>
                            </div>
                            <div class="thinking-content show">
                                <div class="thinking-steps"></div>
                            </div>
                        </div>
                        <div class="ai-response-content" style="margin-top: 10px; display: none;">
                            <!-- AI回复内容将在这里显示 -->
                        </div>
                    </div>
                    <div style="font-size: 11px; color: #888; margin-left: 10px; white-space: nowrap;">
                        ${timestamp}
                    </div>
                </div>
            `;
            
            chatBox.appendChild(messageDiv);
            chatBox.scrollTop = chatBox.scrollHeight;
            
            return messageId;
        }

        // 创建思考过程显示
        function createThinkingProcess(messageId) {
            const messageElement = document.getElementById(messageId);
            if (!messageElement) return;
            
            const thinkingDiv = document.createElement('div');
            thinkingDiv.className = 'thinking-process';
            thinkingDiv.innerHTML = `
                <div class="thinking-header" onclick="toggleThinkingProcess('${messageId}')">
                    <span class="thinking-spinner-text">💭 Thinking...</span>
                    <span class="thinking-toggle">▼</span>
                </div>
                <div class="thinking-content show">
                    <div class="thinking-steps"></div>
                </div>
            `;
            
            // 插入到AI消息内容之前
            const responseContent = messageElement.querySelector('.ai-response-content');
            messageElement.insertBefore(thinkingDiv, responseContent);
            
            return thinkingDiv;
        }

        // 添加思考步骤
        function addThinkingStep(messageId, stepId, status, message, details = null) {
            const stepsContainer = document.querySelector(`#${messageId} .thinking-steps`);
            const stepDiv = document.createElement('div');
            stepDiv.className = `thinking-step ${status.toLowerCase()}`;
            stepDiv.setAttribute('data-step', stepId);
            
            const stepIcon = document.createElement('span');
            stepIcon.className = 'thinking-step-icon';
            stepIcon.textContent = stepId.charAt(0).toUpperCase();
            
            const stepContent = document.createElement('span');
            stepContent.className = 'thinking-step-content';
            stepContent.textContent = message;
            
            if (details) {
                const codePreview = document.createElement('div');
                codePreview.className = 'thinking-code-preview';
                codePreview.textContent = details;
                stepContent.appendChild(codePreview);
            }
            
            stepDiv.appendChild(stepIcon);
            stepDiv.appendChild(stepContent);
            
            stepsContainer.appendChild(stepDiv);
        }

        // 更新思考步骤状态
        function updateThinkingStep(messageId, stepId, status, message, details = null) {
            const step = document.querySelector(`#${messageId} .thinking-step[data-step="${stepId}"]`);
            if (step) {
                step.className = `thinking-step ${status.toLowerCase()}`;
                step.innerHTML = `
                    <span class="thinking-step-icon">${stepId.charAt(0).toUpperCase()}</span>
                    <span class="thinking-step-content">${message}</span>
                `;
                
                if (details) {
                    const codePreview = step.querySelector('.thinking-code-preview');
                    if (codePreview) {
                        codePreview.textContent = details;
                    } else {
                        const newCodePreview = document.createElement('div');
                        newCodePreview.className = 'thinking-code-preview';
                        newCodePreview.textContent = details;
                        step.appendChild(newCodePreview);
                    }
                }
            }
        }

        // 完成思考过程并自动折叠
        function completeThinkingProcess(messageId, duration) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                const thinkingDiv = messageElement.querySelector('.thinking-process');
                if (thinkingDiv) {
                    thinkingDiv.classList.add('completed');
                    const thinkingHeader = thinkingDiv.querySelector('.thinking-header');
                    if (thinkingHeader) {
                        thinkingHeader.innerHTML = `
                            <span class="thinking-spinner-text">💭 Thinking...</span>
                            <span class="thinking-toggle">▼</span>
                        `;
                    }
                }
            }
            
            const thinkingContent = messageElement.querySelector('.thinking-content');
            if (thinkingContent) {
                thinkingContent.classList.add('completed');
            }
            
            const thinkingSteps = messageElement.querySelectorAll('.thinking-step');
            thinkingSteps.forEach(step => step.classList.add('completed'));
            
            const thinkingHeader = messageElement.querySelector('.thinking-header');
            if (thinkingHeader) {
                thinkingHeader.classList.add('completed');
            }
            
            const thinkingToggle = messageElement.querySelector('.thinking-toggle');
            if (thinkingToggle) {
                thinkingToggle.classList.add('completed');
            }
            
            const thinkingTime = messageElement.querySelector('.thinking-time');
            if (thinkingTime) {
                thinkingTime.textContent = `完成时间: ${duration}秒`;
            }
        }

        // 更新AI消息内容
        function updateAIMessageContent(messageId, content) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                const aiResponseContent = messageElement.querySelector('.ai-response-content');
                if (aiResponseContent) {
                    aiResponseContent.innerHTML = content;
                }
            }
        }

        // 添加消息到聊天框
        function addMessageToChat(sender, message) {
            const chatBox = document.getElementById('chatBox');
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${sender}`;
            
            // 格式化时间戳
            const timestamp = new Date().toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit' 
            });
            
            messageDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div style="flex: 1;">
                        <strong>${sender === 'user' ? '👤 您:' : '🤖 AI助手:'}</strong>
                        <div class="message">${message}</div>
                    </div>
                    <div style="font-size: 11px; color: #888; margin-left: 10px; white-space: nowrap;">
                        ${timestamp}
                    </div>
                </div>
            `;
            
            chatBox.appendChild(messageDiv);
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        // 创建新对话
        function createNewConversation() {
            // 实现创建新对话的逻辑
            console.log('创建新对话');
        }

        // 加载对话历史
        function loadConversations() {
            // 实现加载对话历史的逻辑
            console.log('加载对话历史');
        }
    </script>
</body>
</html>